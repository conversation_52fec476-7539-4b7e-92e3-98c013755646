// Firebase configuration and initialization
import { FirebaseApp } from '@react-native-firebase/app';

// This file ensures Firebase is properly configured
// The actual configuration is handled by the native Firebase SDKs
// using google-services.json (Android) and GoogleService-Info.plist (iOS)

export const checkFirebaseConfig = (): boolean => {
  try {
    // Basic check to ensure Firebase is available
    const firebase = require('@react-native-firebase/app');
    return firebase && typeof firebase.default === 'function';
  } catch (error) {
    console.error('Firebase configuration error:', error);
    return false;
  }
};

export default checkFirebaseConfig;
