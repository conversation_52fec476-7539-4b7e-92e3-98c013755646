import React from 'react';
import { render, screen } from '@testing-library/react-native';
import { AuthProvider } from './contexts/AuthContext';
import { LoginScreen } from './screens/LoginScreen';

// Mock Firebase Auth
jest.mock('@react-native-firebase/auth', () => {
  return () => ({
    onAuthStateChanged: jest.fn(() => jest.fn()),
    signInWithEmailAndPassword: jest.fn(),
    createUserWithEmailAndPassword: jest.fn(),
    signOut: jest.fn(),
    sendPasswordResetEmail: jest.fn(),
  });
});

// Mock React Navigation
const mockNavigation = {
  navigate: jest.fn(),
  goBack: jest.fn(),
  reset: jest.fn(),
};

describe('Authentication Setup', () => {
  it('should render login screen correctly', () => {
    render(
      <AuthProvider>
        <LoginScreen navigation={mockNavigation} />
      </AuthProvider>
    );

    expect(screen.getByText('Welcome Back')).toBeTruthy();
    expect(screen.getByText('Sign in to your account')).toBeTruthy();
    expect(screen.getByPlaceholderText('Enter your email')).toBeTruthy();
    expect(screen.getByPlaceholderText('Enter your password')).toBeTruthy();
    expect(screen.getByText('Sign In')).toBeTruthy();
  });

  it('should have proper navigation structure', () => {
    // Test that our navigation components are properly structured
    expect(typeof AuthProvider).toBe('function');
    expect(typeof LoginScreen).toBe('function');
  });
});
