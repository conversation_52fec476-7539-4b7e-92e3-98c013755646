PODS:
  - boost (1.84.0)
  - DoubleConversion (1.1.6)
  - EXConstants (17.0.8):
    - ExpoModulesCore
  - Expo (52.0.47):
    - ExpoModulesCore
  - ExpoAsset (11.0.5):
    - ExpoModulesCore
  - ExpoFileSystem (18.0.12):
    - ExpoModulesCore
  - ExpoFont (13.0.4):
    - ExpoModulesCore
  - ExpoKeepAwake (14.0.3):
    - ExpoModulesCore
  - ExpoModulesCore (2.2.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - fast_float (6.1.4)
  - FBLazyVector (0.76.9)
  - fmt (11.0.2)
  - glog (0.3.5)
  - hermes-engine (0.76.9):
    - hermes-engine/Pre-built (= 0.76.9)
  - hermes-engine/Pre-built (0.76.9)
  - RCT-Folly (2024.10.14.00):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly/Default (= 2024.10.14.00)
  - RCT-Folly/Default (2024.10.14.00):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
  - RCT-Folly/Fabric (2024.10.14.00):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
  - RCTDeprecation (0.76.9)
  - RCTRequired (0.76.9)
  - RCTTypeSafety (0.76.9):
    - FBLazyVector (= 0.76.9)
    - RCTRequired (= 0.76.9)
    - React-Core (= 0.76.9)
  - React (0.76.9):
    - React-Core (= 0.76.9)
    - React-Core/DevSupport (= 0.76.9)
    - React-Core/RCTWebSocket (= 0.76.9)
    - React-RCTActionSheet (= 0.76.9)
    - React-RCTAnimation (= 0.76.9)
    - React-RCTBlob (= 0.76.9)
    - React-RCTImage (= 0.76.9)
    - React-RCTLinking (= 0.76.9)
    - React-RCTNetwork (= 0.76.9)
    - React-RCTSettings (= 0.76.9)
    - React-RCTText (= 0.76.9)
    - React-RCTVibration (= 0.76.9)
  - React-callinvoker (0.76.9)
  - React-Core (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.9)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/Default (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/DevSupport (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.9)
    - React-Core/RCTWebSocket (= 0.76.9)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTWebSocket (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.9)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-CoreModules (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - RCT-Folly
    - RCTTypeSafety
    - React-Core/CoreModulesHeaders
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage
    - ReactCodegen
    - ReactCommon
    - SocketRocket
  - React-cxxreact (0.76.9):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-jsinspector
    - React-logger
    - React-perflogger
    - React-runtimeexecutor
    - React-timing
  - React-debug (0.76.9)
  - React-defaultsnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-domnativemodule
    - React-Fabric
    - React-featureflags
    - React-featureflagsnativemodule
    - React-graphics
    - React-idlecallbacksnativemodule
    - React-ImageManager
    - React-microtasksnativemodule
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-domnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.76.9)
    - React-Fabric/attributedstring (= 0.76.9)
    - React-Fabric/componentregistry (= 0.76.9)
    - React-Fabric/componentregistrynative (= 0.76.9)
    - React-Fabric/components (= 0.76.9)
    - React-Fabric/core (= 0.76.9)
    - React-Fabric/dom (= 0.76.9)
    - React-Fabric/imagemanager (= 0.76.9)
    - React-Fabric/leakchecker (= 0.76.9)
    - React-Fabric/mounting (= 0.76.9)
    - React-Fabric/observers (= 0.76.9)
    - React-Fabric/scheduler (= 0.76.9)
    - React-Fabric/telemetry (= 0.76.9)
    - React-Fabric/templateprocessor (= 0.76.9)
    - React-Fabric/uimanager (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.76.9)
    - React-Fabric/components/root (= 0.76.9)
    - React-Fabric/components/view (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/dom (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers/events (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager/consistency (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricComponents (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.76.9)
    - React-FabricComponents/textlayoutmanager (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.76.9)
    - React-FabricComponents/components/iostextinput (= 0.76.9)
    - React-FabricComponents/components/modal (= 0.76.9)
    - React-FabricComponents/components/rncore (= 0.76.9)
    - React-FabricComponents/components/safeareaview (= 0.76.9)
    - React-FabricComponents/components/scrollview (= 0.76.9)
    - React-FabricComponents/components/text (= 0.76.9)
    - React-FabricComponents/components/textinput (= 0.76.9)
    - React-FabricComponents/components/unimplementedview (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/iostextinput (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/modal (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/rncore (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/safeareaview (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/scrollview (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/text (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/textinput (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricImage (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-featureflags (0.76.9)
  - React-featureflagsnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-graphics (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly/Fabric
    - React-jsi
    - React-jsiexecutor
    - React-utils
  - React-hermes (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimeexecutor
  - React-idlecallbacksnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-ImageManager (0.76.9):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-cxxreact
    - React-debug
    - React-jsi
  - React-jsi (0.76.9):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
  - React-jsiexecutor (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-perflogger
  - React-jsinspector (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - React-featureflags
    - React-jsi
    - React-perflogger
    - React-runtimeexecutor
  - React-jsitracing (0.76.9):
    - React-jsi
  - React-logger (0.76.9):
    - glog
  - React-Mapbuffer (0.76.9):
    - glog
    - React-debug
  - React-microtasksnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-nativeconfig (0.76.9)
  - React-NativeModulesApple (0.76.9):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.76.9):
    - DoubleConversion
    - RCT-Folly (= 2024.10.14.00)
  - React-performancetimeline (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - React-cxxreact
    - React-timing
  - React-RCTActionSheet (0.76.9):
    - React-Core/RCTActionSheetHeaders (= 0.76.9)
  - React-RCTAnimation (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTAppDelegate (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon
  - React-RCTBlob (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCodegen
    - ReactCommon
  - React-RCTFabric (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-nativeconfig
    - React-performancetimeline
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCodegen
    - ReactCommon
  - React-RCTLinking (0.76.9):
    - React-Core/RCTLinkingHeaders (= 0.76.9)
    - React-jsi (= 0.76.9)
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.76.9)
  - React-RCTNetwork (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTSettings (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTText (0.76.9):
    - React-Core/RCTTextHeaders (= 0.76.9)
    - Yoga
  - React-RCTVibration (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-rendererconsistency (0.76.9)
  - React-rendererdebug (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - RCT-Folly
    - React-debug
  - React-rncore (0.76.9)
  - React-RuntimeApple (0.76.9):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
  - React-RuntimeCore (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-runtimeexecutor (0.76.9):
    - React-jsi (= 0.76.9)
  - React-RuntimeHermes (0.76.9):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsitracing
    - React-nativeconfig
    - React-RuntimeCore
    - React-utils
  - React-runtimescheduler (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
  - React-timing (0.76.9)
  - React-utils (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - React-debug
    - React-jsi (= 0.76.9)
  - ReactCodegen (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - ReactCommon (0.76.9):
    - ReactCommon/turbomodule (= 0.76.9)
  - ReactCommon/turbomodule (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-cxxreact
    - React-jsi
    - React-logger
    - React-perflogger
    - ReactCommon/turbomodule/bridging (= 0.76.9)
    - ReactCommon/turbomodule/core (= 0.76.9)
  - ReactCommon/turbomodule/bridging (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-cxxreact
    - React-jsi (= 0.76.9)
    - React-logger
    - React-perflogger
  - ReactCommon/turbomodule/core (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-cxxreact
    - React-debug (= 0.76.9)
    - React-featureflags (= 0.76.9)
    - React-jsi
    - React-logger
    - React-perflogger
    - React-utils (= 0.76.9)
  - RNSVG (15.8.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNSVG/common (= 15.8.0)
    - Yoga
  - RNSVG/common (15.8.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - SocketRocket (0.7.1)
  - Yoga (0.0.0)

DEPENDENCIES:
  - boost (from `../../../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EXConstants (from `../../../node_modules/expo-constants/ios`)
  - Expo (from `../../../node_modules/expo`)
  - ExpoAsset (from `../../../node_modules/expo-asset/ios`)
  - ExpoFileSystem (from `../../../node_modules/expo-file-system/ios`)
  - ExpoFont (from `../../../node_modules/expo-font/ios`)
  - ExpoKeepAwake (from `../../../node_modules/expo-keep-awake/ios`)
  - ExpoModulesCore (from `../../../node_modules/expo-modules-core`)
  - fast_float (from `../../../node_modules/react-native/third-party-podspecs/fast_float.podspec`)
  - FBLazyVector (from `../../../node_modules/react-native/Libraries/FBLazyVector`)
  - fmt (from `../../../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../../../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../../../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - RCT-Folly (from `../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../../../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../../../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../../../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../../../node_modules/react-native/`)
  - React-callinvoker (from `../../../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../../../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../../../node_modules/react-native/`)
  - React-CoreModules (from `../../../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../../../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../../../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../../../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../../../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../../../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../../../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../../../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../../../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../../../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../../../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../../../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../../../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../../../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../../../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../../../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../../../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../../../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsitracing (from `../../../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../../../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../../../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../../../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - React-nativeconfig (from `../../../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../../../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../../../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../../../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../../../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../../../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../../../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../../../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../../../node_modules/react-native/React`)
  - React-RCTImage (from `../../../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../../../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../../../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../../../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../../../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../../../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../../../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-rendererdebug (from `../../../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../../../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../../../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../../../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../../../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../../../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../../../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../../../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../../../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../../../node_modules/react-native/ReactCommon`)
  - RNSVG (from `../../../node_modules/react-native-svg`)
  - Yoga (from `../../../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EXConstants:
    :path: "../../../node_modules/expo-constants/ios"
  Expo:
    :path: "../../../node_modules/expo"
  ExpoAsset:
    :path: "../../../node_modules/expo-asset/ios"
  ExpoFileSystem:
    :path: "../../../node_modules/expo-file-system/ios"
  ExpoFont:
    :path: "../../../node_modules/expo-font/ios"
  ExpoKeepAwake:
    :path: "../../../node_modules/expo-keep-awake/ios"
  ExpoModulesCore:
    :path: "../../../node_modules/expo-modules-core"
  fast_float:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/fast_float.podspec"
  FBLazyVector:
    :path: "../../../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../../../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-11-12-RNv0.76.2-5b4aa20c719830dcf5684832b89a6edb95ac3d64
  RCT-Folly:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../../../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../../../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../../../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../../../node_modules/react-native/"
  React-callinvoker:
    :path: "../../../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../../../node_modules/react-native/"
  React-CoreModules:
    :path: "../../../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../../../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../../../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../../../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../../../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../../../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../../../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../../../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../../../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../../../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../../../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../../../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../../../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsitracing:
    :path: "../../../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../../../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../../../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  React-nativeconfig:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../../../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../../../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../../../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../../../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../../../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../../../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../../../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../../../node_modules/react-native/React"
  React-RCTImage:
    :path: "../../../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../../../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../../../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../../../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../../../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../../../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-rendererdebug:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../../../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../../../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../../../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../../../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../../../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../../../node_modules/react-native/ReactCommon/react/utils"
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../../../node_modules/react-native/ReactCommon"
  RNSVG:
    :path: "../../../node_modules/react-native-svg"
  Yoga:
    :path: "../../../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 1dca942403ed9342f98334bf4c3621f011aa7946
  DoubleConversion: f16ae600a246532c4020132d54af21d0ddb2a385
  EXConstants: a1f35b9aabbb3c6791f8e67722579b1ffcdd3f18
  Expo: 3ddf0d76db6b3eef5d1d9f869676d3b69d95305f
  ExpoAsset: 0687fe05f5d051c4a34dd1f9440bd00858413cfe
  ExpoFileSystem: c8c19bf80d914c83dda3beb8569d7fb603be0970
  ExpoFont: 773955186469acc5108ff569712a2d243857475f
  ExpoKeepAwake: 2a5f15dd4964cba8002c9a36676319a3394c85c7
  ExpoModulesCore: dd965804a882f1dbb6036fceea4d912461aeaa0d
  fast_float: 06eeec4fe712a76acc9376682e4808b05ce978b6
  FBLazyVector: 7605ea4810e0e10ae4815292433c09bf4324ba45
  fmt: 01b82d4ca6470831d1cc0852a1af644be019e8f6
  glog: 08b301085f15bcbb6ff8632a8ebaf239aae04e6a
  hermes-engine: 9e868dc7be781364296d6ee2f56d0c1a9ef0bb11
  RCT-Folly: 7b4f73a92ad9571b9dbdb05bb30fad927fa971e1
  RCTDeprecation: ebe712bb05077934b16c6bf25228bdec34b64f83
  RCTRequired: ca91e5dd26b64f577b528044c962baf171c6b716
  RCTTypeSafety: e7678bd60850ca5a41df9b8dc7154638cb66871f
  React: 4641770499c39f45d4e7cde1eba30e081f9d8a3d
  React-callinvoker: 4bef67b5c7f3f68db5929ab6a4d44b8a002998ea
  React-Core: 0a06707a0b34982efc4a556aff5dae4b22863455
  React-CoreModules: 907334e94314189c2e5eed4877f3efe7b26d85b0
  React-cxxreact: 3a1d5e8f4faa5e09be26614e9c8bbcae8d11b73d
  React-debug: 817160c07dc8d24d020fbd1eac7b3558ffc08964
  React-defaultsnativemodule: 814830ccbc3fb08d67d0190e63b179ee4098c67b
  React-domnativemodule: 270acf94bd0960b026bc3bfb327e703665d27fb4
  React-Fabric: 64586dc191fc1c170372a638b8e722e4f1d0a09b
  React-FabricComponents: b0ebd032387468ea700574c581b139f57a7497fb
  React-FabricImage: 81f0e0794caf25ad1224fa406d288fbc1986607f
  React-featureflags: f2792b067a351d86fdc7bec23db3b9a2f2c8d26c
  React-featureflagsnativemodule: 0d7091ae344d6160c0557048e127897654a5c00f
  React-graphics: cbebe910e4a15b65b0bff94a4d3ed278894d6386
  React-hermes: ec18c10f5a69d49fb9b5e17ae95494e9ea13d4d3
  React-idlecallbacksnativemodule: 6b84add48971da9c40403bd1860d4896462590f2
  React-ImageManager: f2a4c01c2ccb2193e60a20c135da74c7ca4d36f2
  React-jserrorhandler: 61d205b5a7cbc57fed3371dd7eed48c97f49fc64
  React-jsi: 95f7676103137861b79b0f319467627bcfa629ee
  React-jsiexecutor: 41e0fe87cda9ea3970ffb872ef10f1ff8dbd1932
  React-jsinspector: 15578208796723e5c6f39069b6e8bf36863ef6e2
  React-jsitracing: 3758cdb155ea7711f0e77952572ea62d90c69f0b
  React-logger: dbca7bdfd4aa5ef69431362bde6b36d49403cb20
  React-Mapbuffer: 6efad4a606c1fae7e4a93385ee096681ef0300dc
  React-microtasksnativemodule: a645237a841d733861c70b69908ab4a1707b52ad
  React-nativeconfig: 8efdb1ef1e9158c77098a93085438f7e7b463678
  React-NativeModulesApple: 958d4f6c5c2ace4c0f427cf7ef82e28ae6538a22
  React-perflogger: 9b4f13c0afe56bc7b4a0e93ec74b1150421ee22d
  React-performancetimeline: 359db1cb889aa0282fafc5838331b0987c4915a9
  React-RCTActionSheet: aacf2375084dea6e7c221f4a727e579f732ff342
  React-RCTAnimation: d8c82deebebe3aaf7a843affac1b57cb2dc073d4
  React-RCTAppDelegate: 1774aa421a29a41a704ecaf789811ef73c4634b6
  React-RCTBlob: 70a58c11a6a3500d1a12f2e51ca4f6c99babcff8
  React-RCTFabric: 731cda82aed592aacce2d32ead69d78cde5d9274
  React-RCTImage: 5e9d655ba6a790c31e3176016f9b47fd0978fbf0
  React-RCTLinking: 2a48338252805091f7521eaf92687206401bdf2a
  React-RCTNetwork: 0c1282b377257f6b1c81934f72d8a1d0c010e4c3
  React-RCTSettings: f757b679a74e5962be64ea08d7865a7debd67b40
  React-RCTText: e7d20c490b407d3b4a2daa48db4bcd8ec1032af2
  React-RCTVibration: 8228e37144ca3122a91f1de16ba8e0707159cfec
  React-rendererconsistency: b4917053ecbaa91469c67a4319701c9dc0d40be6
  React-rendererdebug: 81becbc8852b38d9b1b68672aa504556481330d5
  React-rncore: 120d21715c9b4ba8f798bffe986cb769b988dd74
  React-RuntimeApple: 52ed0e9e84a7c2607a901149fb13599a3c057655
  React-RuntimeCore: ca6189d2e53d86db826e2673fe8af6571b8be157
  React-runtimeexecutor: 877596f82f5632d073e121cba2d2084b76a76899
  React-RuntimeHermes: 3b752dc5d8a1661c9d1687391d6d96acfa385549
  React-runtimescheduler: 8321bb09175ace2a4f0b3e3834637eb85bf42ebe
  React-timing: 331cbf9f2668c67faddfd2e46bb7f41cbd9320b9
  React-utils: 54df9ada708578c8ad40d92895d6fed03e0e8a9e
  ReactCodegen: a044839eb002996e1830338f998bc9654c306b34
  ReactCommon: bfd3600989d79bc3acbe7704161b171a1480b9fd
  RNSVG: 81d52481cde97ce0dcc81a55b0310723817088d0
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  Yoga: 40f19fff64dce86773bf8b602c7070796c007970

PODFILE CHECKSUM: 642957b49112617ac825b98c6ccfed4782feaad5

COCOAPODS: 1.15.2
