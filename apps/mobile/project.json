{"name": "mobile", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/mobile/src", "projectType": "application", "tags": [], "// targets": "to see all targets run: nx show project mobile --web", "targets": {"emulator": {"command": "emulator -avd android -no-snapshot"}, "logcat": {"command": "adb logcat '*:S' ReactNative:V ReactNativeJS:V"}, "signingreport": {"command": "cd apps/mobile/android && ./gradlew signingreport"}, "clean-gradle": {"command": "cd apps/mobile/android && ./gradlew --stop && ./gradlew clean"}, "redux-devtool": {"command": "redux-devtools --open=electron --hostname=localhost"}, "build-android": {"executor": "@nx/react-native:build-android", "outputs": ["{projectRoot}/build/outputs/bundle", "{projectRoot}/build/outputs/apk"], "options": {"interactive": true}}, "run-android": {"executor": "@nx/react-native:run-android", "options": {"resetCache": true, "interactive": true}}}}