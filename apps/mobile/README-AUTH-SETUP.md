# React Native Authentication Setup

## Overview
This setup includes React Native Navigation and Firebase Authentication for your NX monorepo mobile app.

## What's Been Implemented

### 1. Navigation Structure
- **AuthNavigator**: Handles unauthenticated screens (Login, Register, ForgotPassword)
- **AppNavigator**: Handles authenticated screens (Home)
- **RootNavigator**: Main navigator that switches between Auth and App based on user state

### 2. Authentication Context
- **AuthContext**: Manages authentication state and Firebase auth methods
- **AuthContext.simple**: Temporary mock version for testing without Firebase

### 3. Screens
- **LoginScreen**: Email/password login with validation
- **RegisterScreen**: User registration with password confirmation
- **ForgotPasswordScreen**: Password reset functionality
- **HomeScreen**: Simple authenticated user dashboard

### 4. Firebase Configuration
- Android: `google-services.json` configured
- iOS: `GoogleService-Info.plist` created (needs real Firebase config)
- Native platform configuration updated for Firebase

## Current Status
Currently using the simple mock authentication (`AuthContext.simple`) to avoid Firebase initialization issues.

## To Switch to Real Firebase Authentication

1. **Update all imports** from `AuthContext.simple` back to `AuthContext`:
   ```typescript
   // In all files, change:
   import { useAuth } from '../contexts/AuthContext.simple';
   // Back to:
   import { useAuth } from '../contexts/AuthContext';
   ```

2. **Update Firebase Configuration Files**:
   - Replace `apps/mobile/ios/Mobile/GoogleService-Info.plist` with your real Firebase iOS config
   - Verify `apps/mobile/android/app/google-services.json` has correct Firebase Android config

3. **Enable Authentication in Firebase Console**:
   - Go to Firebase Console > Authentication > Sign-in method
   - Enable Email/Password authentication

## Files Created/Modified

### New Files:
- `src/contexts/AuthContext.tsx` - Firebase authentication context
- `src/contexts/AuthContext.simple.tsx` - Mock authentication for testing
- `src/screens/LoginScreen.tsx` - Login screen
- `src/screens/RegisterScreen.tsx` - Registration screen
- `src/screens/ForgotPasswordScreen.tsx` - Password reset screen
- `src/screens/HomeScreen.tsx` - Authenticated home screen
- `src/navigation/AuthNavigator.tsx` - Authentication flow navigator
- `src/navigation/AppNavigator.tsx` - Authenticated app navigator
- `src/navigation/RootNavigator.tsx` - Main navigation controller
- `src/firebase/config.ts` - Firebase configuration helper
- `ios/Mobile/GoogleService-Info.plist` - iOS Firebase config (placeholder)

### Modified Files:
- `src/app/App.tsx` - Updated to use authentication and navigation
- `android/build.gradle` - Added Google Services plugin
- `android/app/build.gradle` - Added Google Services plugin
- `ios/Mobile/AppDelegate.mm` - Added Firebase initialization

## Dependencies Installed
- `@react-navigation/native`
- `@react-navigation/stack`
- `@react-navigation/bottom-tabs`
- `react-native-screens`
- `react-native-safe-area-context`
- `react-native-gesture-handler`
- `@react-native-firebase/app`
- `@react-native-firebase/auth`

## Running the App
1. For iOS: `npx nx run-ios mobile`
2. For Android: `npx nx run-android mobile`

## Testing the Authentication Flow
1. Start with the Login screen
2. Try registering a new account
3. Test login with created credentials
4. Test password reset functionality
5. Test logout from the Home screen

## Next Steps
1. Replace mock authentication with real Firebase
2. Add proper error handling and user feedback
3. Add loading states and better UX
4. Implement additional authentication methods (Google, Apple, etc.)
5. Add user profile management
6. Implement proper navigation guards
