{"name": "@characterise/mobile", "version": "0.0.1", "private": true, "dependencies": {"@nx/eslint-plugin": "*", "react-native": "*", "@react-native/metro-config": "*", "react-native-svg-transformer": "*", "react": "*", "@testing-library/react-native": "*", "react-native-svg": "*", "react-dom": "*", "@testing-library/jest-native": "*", "vite": "*", "@vitejs/plugin-react": "*", "@nx/vite": "*", "esbuild": "*", "expo": "~52.0.0", "@react-native-firebase/auth": "*", "@react-native-firebase/app": "*", "@react-navigation/stack": "*", "@react-navigation/native": "*", "@react-navigation/native-stack": "*"}, "devDependencies": {}}