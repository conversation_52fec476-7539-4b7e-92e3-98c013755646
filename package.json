{"name": "@characterise/source", "version": "0.0.0", "license": "MIT", "scripts": {}, "private": true, "dependencies": {"@react-native-firebase/app": "^22.4.0", "@react-native-firebase/auth": "^22.4.0", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^7.1.16", "@react-navigation/stack": "^7.4.4", "expo": "~52.0.0", "react": "~18.3.1", "react-dom": "~18.3.1", "react-native": "~0.76.3", "react-native-gesture-handler": "^2.27.2", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1"}, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@eslint/js": "^9.8.0", "@nx/eslint": "21.3.8", "@nx/eslint-plugin": "21.3.8", "@nx/jest": "21.3.8", "@nx/js": "21.3.8", "@nx/react-native": "21.3.8", "@nx/vite": "21.3.8", "@nx/web": "21.3.8", "@nx/workspace": "21.3.8", "@react-native-community/cli": "~15.0.1", "@react-native-community/cli-platform-android": "~15.0.1", "@react-native-community/cli-platform-ios": "~15.0.1", "@react-native/babel-preset": "~0.76.3", "@react-native/metro-config": "~0.76.3", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/jest-native": "~5.4.3", "@testing-library/react-native": "~12.9.0", "@types/jest": "^30.0.0", "@types/node": "18.16.9", "@types/react": "~18.3.12", "@types/react-dom": "~18.3.1", "@vitejs/plugin-react": "^4.2.0", "@vitest/ui": "^3.0.0", "babel-jest": "^30.0.2", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "jest-react-native": "18.0.0", "jest-util": "^30.0.2", "jiti": "2.4.2", "jsdom": "~22.1.0", "nx": "21.3.8", "prettier": "^2.6.2", "react-native-svg": "~15.8.0", "react-native-svg-transformer": "~1.5.0", "react-native-svg-web": "~1.0.9", "react-native-web": "~0.19.13", "react-test-renderer": "~18.3.1", "ts-jest": "^29.4.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.8.2", "typescript-eslint": "^8.29.0", "vite": "^6.0.0", "vitest": "^3.0.0"}, "workspaces": ["apps/*"]}